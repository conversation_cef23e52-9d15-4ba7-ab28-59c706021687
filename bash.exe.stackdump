Stack trace:
Frame         Function      Args
0007FFFF9F90  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8E90) msys-2.0.dll+0x1FE8E
0007FFFF9F90  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA268) msys-2.0.dll+0x67F9
0007FFFF9F90  000210046832 (000210286019, 0007FFFF9E48, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9F90  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9F90  000210068E24 (0007FFFF9FA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA270  00021006A225 (0007FFFF9FA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFAA25A0000 ntdll.dll
7FFAA2050000 KERNEL32.DLL
7FFA9F990000 KERNELBASE.dll
7FFAA0C20000 USER32.dll
7FFA9F830000 win32u.dll
000210040000 msys-2.0.dll
7FFAA0750000 GDI32.dll
7FFA9F6F0000 gdi32full.dll
7FFAA0110000 msvcp_win.dll
7FFA9FFC0000 ucrtbase.dll
7FFAA03F0000 advapi32.dll
7FFAA24B0000 msvcrt.dll
7FFAA21D0000 sechost.dll
7FFAA1F10000 RPCRT4.dll
7FFA9ECD0000 CRYPTBASE.DLL
7FFA9F860000 bcryptPrimitives.dll
7FFAA2290000 IMM32.DLL
